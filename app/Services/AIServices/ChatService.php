<?php

namespace App\Services\AIServices;

use App\Helpers\ClinicQueryHelper;
use App\Helpers\DoctorQueryHelper;
use App\Services\AIServices\AIServiceFactory;
use Illuminate\Support\Facades\Log;

class ChatService
{
    private $analyzer = [
        'provider' => 'google',
        'model' => 'gemma-3-12b-it',
        'max_tokens' => 100,
        'temperature' => 0.1,
    ];

    // private $searcher = [
    //     'provider' => 'google',
    //     'model' => 'gemma-3n-e2b-it',
    //     'max_tokens' => 500,
    //     'temperature' => 0.2,
    // ];

    private $responder = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {

            $analysis = $this->analyzeRequest($userMessage, $context);

            // 2. البحث في البيانات (بدون سياق - بحث في البيانات فقط)
            // $searchResult = $this->searchBusinessData($analysis, $context);

            // 3. إنشاء الرد النهائي (مع السياق - هنا نحتاج المحادثات السابقة)
            $response = $this->generateFinalResponse($userMessage, $analysis, $context);

            return $response;
        } catch (\Exception $e) {
            Log::error('Chat Service Error: ' . $e->getMessage(), [
                'user_message' => $userMessage,
                'context' => $context
            ]);
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * 1. تحليل الطلب
     */
    private function analyzeRequest(string $userMessage, array $context): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);
        $businessData = $context['business_data'] ?? [];

        $prompt = <<<PROMPT
حلل رسالة العميل التالية وافهم ما الذي يريده بدقة، ثم أجب بإجابة مباشرة ومختصرة بناءً على بيانات البزنس الموجودة فقط. لا تضف معلومات من عندك.

رسالة العميل:
"{$userMessage}"

هدفك:
- فهم نية العميل (هل يسأل عن خدمة؟ طبيب؟ عيادة؟ مواعيد؟).
- الرد بناءً على بيانات البزنس فقط.
- لا تشرح، فقط أعطِ المعلومة المطلوبة بشكل مختصر ومنظم.
- إذا لم تتوفر المعلومة في بيانات البزنس، قل: "الطلب غير واضح أو البيانات غير متوفرة."

بيانات البزنس:
{json_encode($businessData, JSON_UNESCAPED_UNICODE)}

أجب فقط بالمطلوب بشكل مباشر.
PROMPT;

        $response = $aiService->chat($prompt, false, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }


    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';


        $prompt = "البيانات المتاحة:\n{$searchResult}\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}";


        $systemMessage = "أنت موظف في {$businessName}.\n";
        $systemMessage .= "مهمتك الرد على استفسارات العملاء بأسلوب ودود، ومفيد.\n";
        $systemMessage .= "- اكتب الرد باللغة العربية، باللهجة السعودية البسيطة.\n";
        $systemMessage .= "- خلك لطيف، واضح، ولا تتجاوز 200 كلمة.\n";
        $systemMessage .= "- لا تكرر المعلومات،والتحيه خاصة لو قد حييت العميل بالرسائل السابقه, وركّز على إفادة العميل بناءً على التحليل.\n";
        $systemMessage .= "- إذا ما كان فيه معلومات كافية، اعتذر بلُطف واقترح طريقة للمساعدة.\n\n";

        $response = $aiService->chat($prompt, true, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $systemMessage
        ]);

        return $response['success'] ? $response['content'] : 'مرحبا عميلنا العزيز لقد تلقينا رسالتك وسيتم الرد بأقرب وقت ممكن';
    }
}
